package main

import (
	"fmt"
	"log"
	"time"
	"gotest/gdb/database"
	"gotest/gdb/models"
	"gotest/gdb/repository"
	"gotest/gdb/service"
)

// ExampleWithService demonstrates using the service layer
func ExampleWithService() {
	fmt.Println("=== User Service Layer Demo ===")

	// Initialize database connection
	config := database.DefaultConfig()
	db, err := database.Connect(config)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer database.Close(db)

	// Initialize repository and service
	userRepo := repository.NewUserRepository(db)
	userService := service.NewUserService(userRepo)

	// 1. Create user with validation
	fmt.Println("\n1. Creating user with service validation...")
	name := "<PERSON>"
	email := "<EMAIL>"
	age := int16(28)
	birthday := time.Date(1995, 3, 20, 0, 0, 0, 0, time.UTC)

	createReq := &models.CreateUserRequest{
		Name:     &name,
		Email:    &email,
		Age:      &age,
		Birthday: &birthday,
	}

	user, err := userService.CreateUser(createReq)
	if err != nil {
		log.Printf("Failed to create user: %v", err)
	} else {
		fmt.Printf("Created user: ID=%d, Name=%s, Email=%s\n", 
			user.ID, *user.Name, *user.Email)
	}

	// 2. Try to create user with duplicate email (should fail)
	fmt.Println("\n2. Trying to create user with duplicate email...")
	duplicateReq := &models.CreateUserRequest{
		Name:  &name,
		Email: &email, // Same email
	}
	_, err = userService.CreateUser(duplicateReq)
	if err != nil {
		fmt.Printf("Expected error: %v\n", err)
	}

	// 3. Try to create user with invalid email (should fail)
	fmt.Println("\n3. Trying to create user with invalid email...")
	invalidEmail := "invalid-email"
	invalidReq := &models.CreateUserRequest{
		Name:  &name,
		Email: &invalidEmail,
	}
	_, err = userService.CreateUser(invalidReq)
	if err != nil {
		fmt.Printf("Expected error: %v\n", err)
	}

	// 4. Search users with age filter
	fmt.Println("\n4. Searching users with age filter...")
	minAge := int16(25)
	maxAge := int16(35)
	searchParams := &models.UserQueryParams{
		MinAge:   &minAge,
		MaxAge:   &maxAge,
		Page:     1,
		PageSize: 10,
	}
	users, total, err := userService.SearchUsers(searchParams)
	if err != nil {
		log.Printf("Failed to search users: %v", err)
	} else {
		fmt.Printf("Found %d users in age range 25-35 (total: %d)\n", len(users), total)
		for _, u := range users {
			fmt.Printf("  - ID=%d, Name=%s, Age=%d\n", 
				u.ID, getStringValue(u.Name), getInt16Value(u.Age))
		}
	}

	// 5. Activate user
	if user != nil {
		fmt.Printf("\n5. Activating user ID %d...\n", user.ID)
		activatedUser, err := userService.ActivateUser(user.ID)
		if err != nil {
			log.Printf("Failed to activate user: %v", err)
		} else {
			fmt.Printf("User activated at: %v\n", activatedUser.ActivatedAt)
		}
	}

	// 6. Get users by age range
	fmt.Println("\n6. Getting users by age range 20-30...")
	ageRangeUsers, err := userService.GetUsersByAgeRange(20, 30)
	if err != nil {
		log.Printf("Failed to get users by age range: %v", err)
	} else {
		fmt.Printf("Found %d users in age range 20-30:\n", len(ageRangeUsers))
		for _, u := range ageRangeUsers {
			fmt.Printf("  - ID=%d, Name=%s, Age=%d\n", 
				u.ID, getStringValue(u.Name), getInt16Value(u.Age))
		}
	}

	// 7. Update user with validation
	if user != nil {
		fmt.Printf("\n7. Updating user ID %d...\n", user.ID)
		newAge := int16(29)
		updateReq := &models.UpdateUserRequest{
			Age: &newAge,
		}
		updatedUser, err := userService.UpdateUser(user.ID, updateReq)
		if err != nil {
			log.Printf("Failed to update user: %v", err)
		} else {
			fmt.Printf("Updated user age to: %d\n", *updatedUser.Age)
		}
	}

	// 8. Try to update with invalid age (should fail)
	if user != nil {
		fmt.Printf("\n8. Trying to update user with invalid age...\n")
		invalidAge := int16(-5)
		invalidUpdateReq := &models.UpdateUserRequest{
			Age: &invalidAge,
		}
		_, err = userService.UpdateUser(user.ID, invalidUpdateReq)
		if err != nil {
			fmt.Printf("Expected error: %v\n", err)
		}
	}

	// 9. Deactivate user
	if user != nil {
		fmt.Printf("\n9. Deactivating user ID %d...\n", user.ID)
		deactivatedUser, err := userService.DeactivateUser(user.ID)
		if err != nil {
			log.Printf("Failed to deactivate user: %v", err)
		} else {
			if deactivatedUser.ActivatedAt == nil {
				fmt.Println("User successfully deactivated")
			} else {
				fmt.Printf("User deactivated, activated_at: %v\n", deactivatedUser.ActivatedAt)
			}
		}
	}

	// 10. Clean up - delete user
	if user != nil {
		fmt.Printf("\n10. Deleting user ID %d...\n", user.ID)
		err = userService.DeleteUser(user.ID)
		if err != nil {
			log.Printf("Failed to delete user: %v", err)
		} else {
			fmt.Println("User deleted successfully")
		}
	}

	fmt.Println("\n=== Service Layer Demo completed ===")
}

// Helper functions to safely get values from pointers
func getStringValue(ptr *string) string {
	if ptr == nil {
		return ""
	}
	return *ptr
}

func getInt16Value(ptr *int16) int16 {
	if ptr == nil {
		return 0
	}
	return *ptr
}
