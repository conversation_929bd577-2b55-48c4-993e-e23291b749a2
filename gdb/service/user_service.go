package service

import (
	"fmt"
	"gotest/gdb/models"
	"gotest/gdb/repository"
	"strings"
	"time"
)

// UserService defines the interface for user business logic
type UserService interface {
	CreateUser(req *models.CreateUserRequest) (*models.User, error)
	GetUserByID(id int64) (*models.User, error)
	GetUserByEmail(email string) (*models.User, error)
	SearchUsers(params *models.UserQueryParams) ([]*models.User, int64, error)
	UpdateUser(id int64, updates *models.UpdateUserRequest) (*models.User, error)
	DeleteUser(id int64) error
	ActivateUser(id int64) (*models.User, error)
	DeactivateUser(id int64) (*models.User, error)
	GetUsersByAgeRange(minAge, maxAge int16) ([]*models.User, error)
}

// userService implements UserService interface
type userService struct {
	userRepo repository.UserRepository
}

// NewUserService creates a new user service instance
func NewUserService(userRepo repository.UserRepository) UserService {
	return &userService{
		userRepo: userRepo,
	}
}

// CreateUser creates a new user with validation
func (s *userService) CreateUser(req *models.CreateUserRequest) (*models.User, error) {
	// Validate required fields
	if req.Email == nil || strings.TrimSpace(*req.Email) == "" {
		return nil, fmt.Errorf("email is required")
	}

	// Validate email format (basic validation)
	if !isValidEmail(*req.Email) {
		return nil, fmt.Errorf("invalid email format")
	}

	// Check if user with email already exists
	existingUser, _ := s.userRepo.GetByEmail(*req.Email)
	if existingUser != nil {
		return nil, fmt.Errorf("user with email %s already exists", *req.Email)
	}

	// Validate age if provided
	if req.Age != nil && *req.Age < 0 {
		return nil, fmt.Errorf("age cannot be negative")
	}

	// Validate birthday if provided
	if req.Birthday != nil && req.Birthday.After(time.Now()) {
		return nil, fmt.Errorf("birthday cannot be in the future")
	}

	return s.userRepo.Create(req)
}

// GetUserByID retrieves a user by ID
func (s *userService) GetUserByID(id int64) (*models.User, error) {
	if id <= 0 {
		return nil, fmt.Errorf("invalid user ID")
	}
	return s.userRepo.GetByID(id)
}

// GetUserByEmail retrieves a user by email
func (s *userService) GetUserByEmail(email string) (*models.User, error) {
	if strings.TrimSpace(email) == "" {
		return nil, fmt.Errorf("email is required")
	}
	return s.userRepo.GetByEmail(email)
}

// SearchUsers searches users with filtering and pagination
func (s *userService) SearchUsers(params *models.UserQueryParams) ([]*models.User, int64, error) {
	// Set default pagination if not provided
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 10
	}
	if params.PageSize > 100 {
		params.PageSize = 100 // Limit max page size
	}

	// Validate age filters
	if params.MinAge != nil && *params.MinAge < 0 {
		return nil, 0, fmt.Errorf("minimum age cannot be negative")
	}
	if params.MaxAge != nil && *params.MaxAge < 0 {
		return nil, 0, fmt.Errorf("maximum age cannot be negative")
	}
	if params.MinAge != nil && params.MaxAge != nil && *params.MinAge > *params.MaxAge {
		return nil, 0, fmt.Errorf("minimum age cannot be greater than maximum age")
	}

	return s.userRepo.List(params)
}

// UpdateUser updates a user with validation
func (s *userService) UpdateUser(id int64, updates *models.UpdateUserRequest) (*models.User, error) {
	if id <= 0 {
		return nil, fmt.Errorf("invalid user ID")
	}

	// Validate email format if provided
	if updates.Email != nil && !isValidEmail(*updates.Email) {
		return nil, fmt.Errorf("invalid email format")
	}

	// Check if email is already taken by another user
	if updates.Email != nil {
		existingUser, _ := s.userRepo.GetByEmail(*updates.Email)
		if existingUser != nil && existingUser.ID != id {
			return nil, fmt.Errorf("email %s is already taken by another user", *updates.Email)
		}
	}

	// Validate age if provided
	if updates.Age != nil && *updates.Age < 0 {
		return nil, fmt.Errorf("age cannot be negative")
	}

	// Validate birthday if provided
	if updates.Birthday != nil && updates.Birthday.After(time.Now()) {
		return nil, fmt.Errorf("birthday cannot be in the future")
	}

	return s.userRepo.Update(id, updates)
}

// DeleteUser deletes a user by ID
func (s *userService) DeleteUser(id int64) error {
	if id <= 0 {
		return fmt.Errorf("invalid user ID")
	}
	return s.userRepo.Delete(id)
}

// ActivateUser activates a user by setting activated_at to current time
func (s *userService) ActivateUser(id int64) (*models.User, error) {
	if id <= 0 {
		return nil, fmt.Errorf("invalid user ID")
	}

	now := time.Now()
	updates := &models.UpdateUserRequest{
		ActivatedAt: &now,
	}

	return s.userRepo.Update(id, updates)
}

// DeactivateUser deactivates a user by setting activated_at to nil
func (s *userService) DeactivateUser(id int64) (*models.User, error) {
	if id <= 0 {
		return nil, fmt.Errorf("invalid user ID")
	}

	updates := &models.UpdateUserRequest{
		ActivatedAt: nil,
	}

	return s.userRepo.Update(id, updates)
}

// GetUsersByAgeRange retrieves users within a specific age range
func (s *userService) GetUsersByAgeRange(minAge, maxAge int16) ([]*models.User, error) {
	if minAge < 0 || maxAge < 0 {
		return nil, fmt.Errorf("age values cannot be negative")
	}
	if minAge > maxAge {
		return nil, fmt.Errorf("minimum age cannot be greater than maximum age")
	}

	params := &models.UserQueryParams{
		MinAge:   &minAge,
		MaxAge:   &maxAge,
		Page:     1,
		PageSize: 1000, // Get all users in range
	}

	users, _, err := s.userRepo.List(params)
	return users, err
}

// isValidEmail performs basic email validation
func isValidEmail(email string) bool {
	email = strings.TrimSpace(email)
	if len(email) == 0 {
		return false
	}
	
	// Basic email validation - contains @ and has parts before and after
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return false
	}
	
	if len(parts[0]) == 0 || len(parts[1]) == 0 {
		return false
	}
	
	// Check if domain part contains at least one dot
	if !strings.Contains(parts[1], ".") {
		return false
	}
	
	return true
}
