package models

import (
	"time"
	"gorm.io/gorm"
)

// User represents the users table structure
type User struct {
	ID           int64      `gorm:"primaryKey;column:id;autoIncrement" json:"id"`
	Name         *string    `gorm:"column:name;type:text" json:"name"`
	Email        *string    `gorm:"column:email;type:text" json:"email"`
	Age          *int16     `gorm:"column:age;type:smallint" json:"age"`
	Birthday     *time.Time `gorm:"column:birthday;type:timestamptz" json:"birthday"`
	MemberNumber *string    `gorm:"column:member_number;type:text" json:"member_number"`
	ActivatedAt  *time.Time `gorm:"column:activated_at;type:timestamptz" json:"activated_at"`
	CreatedAt    *time.Time `gorm:"column:created_at;type:timestamptz;autoCreateTime" json:"created_at"`
	UpdatedAt    *time.Time `gorm:"column:updated_at;type:timestamptz;autoUpdateTime" json:"updated_at"`
}

// TableName specifies the table name for GORM
func (User) TableName() string {
	return "users"
}

// CreateUserRequest represents the request structure for creating a user
type CreateUserRequest struct {
	Name         *string    `json:"name"`
	Email        *string    `json:"email"`
	Age          *int16     `json:"age"`
	Birthday     *time.Time `json:"birthday"`
	MemberNumber *string    `json:"member_number"`
	ActivatedAt  *time.Time `json:"activated_at"`
}

// UpdateUserRequest represents the request structure for updating a user
type UpdateUserRequest struct {
	Name         *string    `json:"name"`
	Email        *string    `json:"email"`
	Age          *int16     `json:"age"`
	Birthday     *time.Time `json:"birthday"`
	MemberNumber *string    `json:"member_number"`
	ActivatedAt  *time.Time `json:"activated_at"`
}

// UserQueryParams represents query parameters for filtering users
type UserQueryParams struct {
	Name     *string `json:"name"`
	Email    *string `json:"email"`
	Age      *int16  `json:"age"`
	MinAge   *int16  `json:"min_age"`
	MaxAge   *int16  `json:"max_age"`
	Page     int     `json:"page"`
	PageSize int     `json:"page_size"`
}
