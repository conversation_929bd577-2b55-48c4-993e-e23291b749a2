package repository

import (
	"fmt"
	"gotest/gdb/models"
	"gorm.io/gorm"
)

// UserRepository defines the interface for user CRUD operations
type UserRepository interface {
	Create(user *models.CreateUserRequest) (*models.User, error)
	GetByID(id int64) (*models.User, error)
	GetByEmail(email string) (*models.User, error)
	GetByNameAndEmail(name, email string) (*models.User, error)
	List(params *models.UserQueryParams) ([]*models.User, int64, error)
	Update(id int64, updates *models.UpdateUserRequest) (*models.User, error)
	Delete(id int64) error
	DeleteByEmail(email string) error
}

// userRepository implements UserRepository interface
type userRepository struct {
	db *gorm.DB
}

// NewUserRepository creates a new user repository instance
func NewUserRepository(db *gorm.DB) UserRepository {
	return &userRepository{db: db}
}

// <PERSON><PERSON> creates a new user
func (r *userRepository) Create(req *models.CreateUserRequest) (*models.User, error) {
	user := &models.User{
		Name:         req.Name,
		Email:        req.Email,
		Age:          req.Age,
		Birthday:     req.Birthday,
		MemberNumber: req.MemberNumber,
		ActivatedAt:  req.ActivatedAt,
	}

	if err := r.db.Create(user).Error; err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	return user, nil
}

// GetByID retrieves a user by ID
func (r *userRepository) GetByID(id int64) (*models.User, error) {
	var user models.User
	if err := r.db.First(&user, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user with ID %d not found", id)
		}
		return nil, fmt.Errorf("failed to get user by ID: %w", err)
	}
	return &user, nil
}

// GetByEmail retrieves a user by email
func (r *userRepository) GetByEmail(email string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("email = ?", email).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user with email %s not found", email)
		}
		return nil, fmt.Errorf("failed to get user by email: %w", err)
	}
	return &user, nil
}

// GetByNameAndEmail retrieves a user by name and email (using the unique index)
func (r *userRepository) GetByNameAndEmail(name, email string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("name = ? AND email = ?", name, email).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user with name %s and email %s not found", name, email)
		}
		return nil, fmt.Errorf("failed to get user by name and email: %w", err)
	}
	return &user, nil
}

// List retrieves users with optional filtering and pagination
func (r *userRepository) List(params *models.UserQueryParams) ([]*models.User, int64, error) {
	var users []*models.User
	var total int64

	query := r.db.Model(&models.User{})

	// Apply filters
	if params.Name != nil {
		query = query.Where("name ILIKE ?", "%"+*params.Name+"%")
	}
	if params.Email != nil {
		query = query.Where("email ILIKE ?", "%"+*params.Email+"%")
	}
	if params.Age != nil {
		query = query.Where("age = ?", *params.Age)
	}
	if params.MinAge != nil {
		query = query.Where("age >= ?", *params.MinAge)
	}
	if params.MaxAge != nil {
		query = query.Where("age <= ?", *params.MaxAge)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count users: %w", err)
	}

	// Apply pagination
	if params.Page > 0 && params.PageSize > 0 {
		offset := (params.Page - 1) * params.PageSize
		query = query.Offset(offset).Limit(params.PageSize)
	}

	// Execute query
	if err := query.Order("created_at DESC").Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list users: %w", err)
	}

	return users, total, nil
}

// Update updates a user by ID
func (r *userRepository) Update(id int64, updates *models.UpdateUserRequest) (*models.User, error) {
	var user models.User
	
	// First, check if user exists
	if err := r.db.First(&user, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user with ID %d not found", id)
		}
		return nil, fmt.Errorf("failed to find user: %w", err)
	}

	// Update fields
	updateData := make(map[string]interface{})
	if updates.Name != nil {
		updateData["name"] = updates.Name
	}
	if updates.Email != nil {
		updateData["email"] = updates.Email
	}
	if updates.Age != nil {
		updateData["age"] = updates.Age
	}
	if updates.Birthday != nil {
		updateData["birthday"] = updates.Birthday
	}
	if updates.MemberNumber != nil {
		updateData["member_number"] = updates.MemberNumber
	}
	if updates.ActivatedAt != nil {
		updateData["activated_at"] = updates.ActivatedAt
	}

	if err := r.db.Model(&user).Updates(updateData).Error; err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return &user, nil
}

// Delete deletes a user by ID
func (r *userRepository) Delete(id int64) error {
	result := r.db.Delete(&models.User{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete user: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("user with ID %d not found", id)
	}
	return nil
}

// DeleteByEmail deletes a user by email
func (r *userRepository) DeleteByEmail(email string) error {
	result := r.db.Where("email = ?", email).Delete(&models.User{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete user by email: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("user with email %s not found", email)
	}
	return nil
}
