package repository

import (
	"testing"
	"time"
	"gotest/gdb/models"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// setupTestDB creates an in-memory SQLite database for testing
func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		t.Fatalf("Failed to connect to test database: %v", err)
	}

	// Auto-migrate the schema
	err = db.AutoMigrate(&models.User{})
	if err != nil {
		t.Fatalf("Failed to migrate test database: %v", err)
	}

	return db
}

func TestUserRepository_Create(t *testing.T) {
	db := setupTestDB(t)
	repo := NewUserRepository(db)

	name := "Test User"
	email := "<EMAIL>"
	age := int16(25)

	req := &models.CreateUserRequest{
		Name:  &name,
		Email: &email,
		Age:   &age,
	}

	user, err := repo.Create(req)
	if err != nil {
		t.Fatalf("Failed to create user: %v", err)
	}

	if user.ID == 0 {
		t.Error("Expected user ID to be set")
	}
	if *user.Name != name {
		t.Errorf("Expected name %s, got %s", name, *user.Name)
	}
	if *user.Email != email {
		t.Errorf("Expected email %s, got %s", email, *user.Email)
	}
	if *user.Age != age {
		t.Errorf("Expected age %d, got %d", age, *user.Age)
	}
}

func TestUserRepository_GetByID(t *testing.T) {
	db := setupTestDB(t)
	repo := NewUserRepository(db)

	// Create a test user first
	name := "Test User"
	email := "<EMAIL>"
	req := &models.CreateUserRequest{
		Name:  &name,
		Email: &email,
	}

	createdUser, err := repo.Create(req)
	if err != nil {
		t.Fatalf("Failed to create test user: %v", err)
	}

	// Test getting the user by ID
	foundUser, err := repo.GetByID(createdUser.ID)
	if err != nil {
		t.Fatalf("Failed to get user by ID: %v", err)
	}

	if foundUser.ID != createdUser.ID {
		t.Errorf("Expected ID %d, got %d", createdUser.ID, foundUser.ID)
	}
	if *foundUser.Name != name {
		t.Errorf("Expected name %s, got %s", name, *foundUser.Name)
	}
}

func TestUserRepository_GetByEmail(t *testing.T) {
	db := setupTestDB(t)
	repo := NewUserRepository(db)

	name := "Test User"
	email := "<EMAIL>"
	req := &models.CreateUserRequest{
		Name:  &name,
		Email: &email,
	}

	_, err := repo.Create(req)
	if err != nil {
		t.Fatalf("Failed to create test user: %v", err)
	}

	// Test getting the user by email
	foundUser, err := repo.GetByEmail(email)
	if err != nil {
		t.Fatalf("Failed to get user by email: %v", err)
	}

	if *foundUser.Email != email {
		t.Errorf("Expected email %s, got %s", email, *foundUser.Email)
	}
	if *foundUser.Name != name {
		t.Errorf("Expected name %s, got %s", name, *foundUser.Name)
	}
}

func TestUserRepository_Update(t *testing.T) {
	db := setupTestDB(t)
	repo := NewUserRepository(db)

	// Create a test user first
	name := "Test User"
	email := "<EMAIL>"
	age := int16(25)
	req := &models.CreateUserRequest{
		Name:  &name,
		Email: &email,
		Age:   &age,
	}

	createdUser, err := repo.Create(req)
	if err != nil {
		t.Fatalf("Failed to create test user: %v", err)
	}

	// Update the user
	newAge := int16(30)
	newName := "Updated User"
	updateReq := &models.UpdateUserRequest{
		Name: &newName,
		Age:  &newAge,
	}

	updatedUser, err := repo.Update(createdUser.ID, updateReq)
	if err != nil {
		t.Fatalf("Failed to update user: %v", err)
	}

	if *updatedUser.Name != newName {
		t.Errorf("Expected name %s, got %s", newName, *updatedUser.Name)
	}
	if *updatedUser.Age != newAge {
		t.Errorf("Expected age %d, got %d", newAge, *updatedUser.Age)
	}
	// Email should remain unchanged
	if *updatedUser.Email != email {
		t.Errorf("Expected email %s, got %s", email, *updatedUser.Email)
	}
}

func TestUserRepository_Delete(t *testing.T) {
	db := setupTestDB(t)
	repo := NewUserRepository(db)

	// Create a test user first
	name := "Test User"
	email := "<EMAIL>"
	req := &models.CreateUserRequest{
		Name:  &name,
		Email: &email,
	}

	createdUser, err := repo.Create(req)
	if err != nil {
		t.Fatalf("Failed to create test user: %v", err)
	}

	// Delete the user
	err = repo.Delete(createdUser.ID)
	if err != nil {
		t.Fatalf("Failed to delete user: %v", err)
	}

	// Try to get the deleted user - should fail
	_, err = repo.GetByID(createdUser.ID)
	if err == nil {
		t.Error("Expected error when getting deleted user, but got none")
	}
}

func TestUserRepository_List(t *testing.T) {
	db := setupTestDB(t)
	repo := NewUserRepository(db)

	// Create multiple test users
	users := []struct {
		name  string
		email string
		age   int16
	}{
		{"User 1", "<EMAIL>", 25},
		{"User 2", "<EMAIL>", 30},
		{"User 3", "<EMAIL>", 35},
	}

	for _, u := range users {
		req := &models.CreateUserRequest{
			Name:  &u.name,
			Email: &u.email,
			Age:   &u.age,
		}
		_, err := repo.Create(req)
		if err != nil {
			t.Fatalf("Failed to create test user %s: %v", u.name, err)
		}
	}

	// Test listing all users
	params := &models.UserQueryParams{
		Page:     1,
		PageSize: 10,
	}
	foundUsers, total, err := repo.List(params)
	if err != nil {
		t.Fatalf("Failed to list users: %v", err)
	}

	if len(foundUsers) != 3 {
		t.Errorf("Expected 3 users, got %d", len(foundUsers))
	}
	if total != 3 {
		t.Errorf("Expected total 3, got %d", total)
	}

	// Test filtering by age
	minAge := int16(30)
	filterParams := &models.UserQueryParams{
		MinAge:   &minAge,
		Page:     1,
		PageSize: 10,
	}
	filteredUsers, filteredTotal, err := repo.List(filterParams)
	if err != nil {
		t.Fatalf("Failed to list filtered users: %v", err)
	}

	if len(filteredUsers) != 2 {
		t.Errorf("Expected 2 users with age >= 30, got %d", len(filteredUsers))
	}
	if filteredTotal != 2 {
		t.Errorf("Expected filtered total 2, got %d", filteredTotal)
	}
}
