# User Management System with GORM

This project implements a complete CRUD (Create, Read, Update, Delete) system for user management using GORM and PostgreSQL.

## Project Structure

```
gdb/
├── main.go                    # Main application with demo examples
├── models/
│   └── user.go               # User model and request/response structures
├── database/
│   └── connection.go         # Database connection configuration
├── repository/
│   └── user_repository.go    # Data access layer with CRUD operations
├── service/
│   └── user_service.go       # Business logic layer with validation
└── README.md                 # This file
```

## Database Schema

The implementation is based on the following PostgreSQL table structure:

```sql
CREATE TABLE public.users (
    id bigserial NOT NULL,
    name text NULL,
    email text NULL,
    age int2 NULL,
    birthday timestamptz NULL,
    member_number text NULL,
    activated_at timestamptz NULL,
    created_at timestamptz NULL,
    updated_at timestamptz NULL,
    CONSTRAINT users_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX name_age_idx ON public.users USING btree (name, email);
```

## Features

### Repository Layer (Data Access)
- **Create**: Add new users to the database
- **Read**: 
  - Get user by ID
  - Get user by email
  - Get user by name and email (using unique index)
  - List users with filtering and pagination
- **Update**: Update user information by ID
- **Delete**: 
  - Delete user by ID
  - Delete user by email

### Service Layer (Business Logic)
- Input validation (email format, age validation, etc.)
- Duplicate email checking
- User activation/deactivation
- Age range queries
- Pagination with limits

### Supported Operations

1. **Create User**
   ```go
   createReq := &models.CreateUserRequest{
       Name:         &name,
       Email:        &email,
       Age:          &age,
       Birthday:     &birthday,
       MemberNumber: &memberNumber,
       ActivatedAt:  &activatedAt,
   }
   user, err := userRepo.Create(createReq)
   ```

2. **Get User by ID**
   ```go
   user, err := userRepo.GetByID(userID)
   ```

3. **Get User by Email**
   ```go
   user, err := userRepo.GetByEmail("<EMAIL>")
   ```

4. **List Users with Filtering**
   ```go
   params := &models.UserQueryParams{
       Name:     &searchName,
       MinAge:   &minAge,
       MaxAge:   &maxAge,
       Page:     1,
       PageSize: 10,
   }
   users, total, err := userRepo.List(params)
   ```

5. **Update User**
   ```go
   updateReq := &models.UpdateUserRequest{
       Age: &newAge,
   }
   user, err := userRepo.Update(userID, updateReq)
   ```

6. **Delete User**
   ```go
   err := userRepo.Delete(userID)
   // or
   err := userRepo.DeleteByEmail("<EMAIL>")
   ```

## Configuration

Update the database configuration in `main.go`:

```go
config := database.DefaultConfig()
config.Host = "your_host"
config.Port = 5432
config.User = "your_username"
config.Password = "your_password"
config.DBName = "your_database"
```

## Running the Application

1. Make sure PostgreSQL is running and the users table exists
2. Update database configuration in `main.go`
3. Run the application:
   ```bash
   cd gdb
   go run main.go
   ```

## Key Features

### Data Types
- Uses appropriate Go types that match PostgreSQL types
- Handles nullable fields with pointers
- Automatic timestamp management for created_at and updated_at

### Validation
- Email format validation
- Age validation (non-negative)
- Birthday validation (not in future)
- Duplicate email prevention

### Pagination
- Configurable page size with maximum limit
- Total count returned with results

### Error Handling
- Comprehensive error messages
- Proper handling of "not found" cases
- Database constraint violations

### Performance
- Uses database indexes effectively
- Efficient queries with proper filtering
- Pagination to handle large datasets

## Dependencies

The project uses the following Go modules:
- `gorm.io/gorm` - ORM framework
- `gorm.io/driver/postgres` - PostgreSQL driver for GORM

These are already included in the main project's go.mod file.
