package main

import (
	"database/sql"
	"fmt"
	"golang.org/x/exp/maps"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/logger"
	"log"
	"os"
	"time"
)

type User struct {
	ID           uint
	Name         string
	Email        *string
	Age          uint8
	Birthday     *time.Time
	MemberNumber sql.NullString
	ActivatedAt  sql.NullTime
	CreatedAt    time.Time
	UpdatedAt    time.Time
}

func main() {
	maps.Copy()
	dsn := "host=********** user=postgres password=teacherschoolpg94 dbname=test port=5436 sslmode=disable TimeZone=Asia/Shanghai"
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags), // io writer（日志输出的目标，前缀和日志包含的内容——译者注）
		logger.Config{
			SlowThreshold:             time.Second, // 慢 SQL 阈值
			LogLevel:                  logger.Info, // 日志级别
			IgnoreRecordNotFoundError: true,        // 忽略ErrRecordNotFound（记录未找到）错误
			Colorful:                  true,        // 禁用彩色打印
		},
	)
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{Logger: newLogger})
	if err != nil {
		panic(err)
	}
	email := "<EMAIL>"
	us := []User{
		User{
			Name:     "张三",
			Email:    &email,
			Age:      0,
			Birthday: nil,
		},
		User{
			Name:     "李四",
			Email:    &email,
			Age:      0,
			Birthday: nil,
		},
	}
	err = db.Clauses(clause.OnConflict{DoNothing: true}).Create(&us).Error
	if err != nil {
		panic(err)
	}
	for i := 0; i < len(us); i++ {
		fmt.Println(us[i].ID)
	}
}
